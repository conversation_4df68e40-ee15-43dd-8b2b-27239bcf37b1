#
# Custom rEFInd Configuration for Multi-Boot System
# Windows + Pop!_OS + Garuda Linux
#

# Timeout in seconds for the main menu screen
timeout 10

# Set the default selection. This can be a volume label, a partial volume 
# name, a filesystem unique ID, or a complete pathname to a loader.
# The default is to boot the previously-booted OS.
default_selection "Microsoft"

# Enable mouse support
enable_mouse

# Set the screen's video resolution. Pass this option either:
#  * two values, corresponding to the X and Y resolutions
#  * one value, corresponding to a GOP (UEFI) video mode
# Note that not all resolutions are supported. On UEFI systems, passing
# an incorrect value results in a message being shown on the screen to
# that effect, along with a list of supported modes. On EFI 1.x systems
# (e.g., Macs), setting an incorrect mode silently fails. On both types
# of systems, setting an incorrect resolution results in the default
# resolution being used. A resolution of 1024x768 usually works, but
# higher values often don't.
# Default is "0 0" (use the system default resolution, usually 800x600).
resolution 1024 768

# Launch specified OSes in graphics mode. By default, rEFInd switches
# to text mode and displays basic pre-launch information when launching
# all OSes except macOS. Using graphics mode can produce a more seamless
# transition, but displays no information, which can make matters
# difficult if you must debug a problem. Also, on at least one known
# computer, using graphics mode prevents a crash when using the Linux
# kernel's EFI stub loader. You can specify an empty list to boot all
# OSes in text mode.
# Valid options:
#   osx     - macOS
#   linux   - A Linux kernel with EFI stub loader
#   elilo   - The ELILO boot loader
#   grub    - The GRUB (Legacy or 2) boot loader
#   windows - Microsoft Windows
# Default value: osx
use_graphics_for osx,linux,windows

# Which non-bootloader tools to show on the tools line, and in what
# order to display them:
#  shell           - the EFI shell (requires external program; see rEFInd
#                    documentation for details)
#  memtest         - the memtest86 program, in EFI/tools, EFI/memtest86,
#                    EFI/memtest, EFI/tools/memtest86, or EFI/tools/memtest
#  gptsync         - the (dangerous) gptsync.efi utility (requires external
#                    program; see rEFInd documentation for details)
#  gdisk           - the gdisk.efi utility (requires external program; see
#                    rEFInd documentation for details)
#  apple_recovery  - boots the Apple Recovery HD partition, if present
#  windows_recovery - boots an OEM Windows recovery tool, if present
#                     (see also the windows_recovery_files option)
#  mok_tool        - makes available the Machine Owner Key (MOK) maintenance
#                    tool, MokManager.efi, used on Secure Boot systems
#  about           - an "about this program" option
#  hidden_tags     - manage hidden tags
#  exit            - a tag to exit from rEFInd
#  shutdown        - shuts down the computer (a bug causes this to reboot
#                    many UEFI systems)
#  reboot          - a tag to reboot the computer
#  firmware        - a tag to reboot the computer into the firmware's
#                    user interface (ignored on older computers)
#  fwupdate        - a tag to update the firmware; launches the fwupx64.efi
#                    (or similar) program
#  netboot         - launch the ipxe.efi tool for network (PXE) booting
# Default is shell,memtest,gdisk,apple_recovery,windows_recovery,mok_tool,about,hidden_tags,shutdown,reboot,firmware
showtools shell,memtest,about,hidden_tags,reboot,firmware

# Directories in which to search for EFI drivers. These drivers can
# provide filesystem support, give access to hard disks on plug-in
# controllers, etc. In most cases none are needed, but if you add
# drivers and you want rEFInd to automatically load them, you should
# specify one or more paths here. rEFInd always scans the "drivers"
# and "drivers_{arch}" subdirectories of its own installation directory
# (where "{arch}" is your architecture code); this option specifies
# ADDITIONAL directories to scan.
# Default is to scan no additional directories for drivers
#scan_driver_dirs EFI/tools/drivers,drivers

# Which types of boot loaders to search, and in what order to display them:
#  internal      - internal EFI disk-based boot loaders
#  external      - external EFI disk-based boot loaders
#  optical       - EFI optical-disc boot loaders
#  netboot       - EFI network (PXE) boot loaders
#  hdbios        - BIOS disk-based boot loaders
#  biosexternal  - BIOS external boot loaders (USB, eSATA, etc.)
#  cd            - BIOS optical-disc boot loaders
#  manual        - use stanzas later in this configuration file
# Note that the legacy BIOS options require the presence of a BIOS-to-EFI
# wrapper layer, such as CSM support, with associated drivers. Also, note
# that BIOS-mode loaders tend to be displayed after (below) EFI-mode loaders.
# This is intended to encourage use of EFI-mode OSes when available.
# Default is internal,external,optical,manual
scanfor internal,external,optical,manual

# Delay for the specified number of seconds before scanning disks.
# This can help some users who find that some of their disks
# (usually external or optical discs) aren't detected initially,
# but are detected after pressing Esc.
# The default is 0.
scan_delay 1

# Set the maximum number of tags that can be displayed on the screen at
# any time. If more loaders are discovered than this value, rEFInd shows
# a subset in a scrolling list. If this value is set too high for the
# screen to handle, it's reduced to the value that the screen can manage.
# If this value is set to 0 (the default), it's adjusted to the number
# that will fit on the screen.
max_tags 0

# Set the default menu selection.  The available arguments match those of
# the "default_selection" option described earlier.
default_selection "Microsoft"

# Include a secondary configuration file within this one. This secondary
# file is loaded as if its options appeared at the point of the "include"
# token in the main file, so options in the secondary file override those
# that precede it, and options that follow the "include" token override
# those in the secondary file. Note that the secondary file may NOT load
# additional configuration files (i.e., includes may not be nested).
# Default is none.
#include manual.conf

# Sample manual configuration stanzas. Each begins with the "menuentry"
# keyword followed by a name that's to appear on the menu (use quotes
# if you want the name to contain a space) and an open curly brace
# ("{"). Each entry ends with a close curly brace ("}"). Common
# keywords within each stanza include:
#
#  volume    - identifies the filesystem from which subsequent files
#              are loaded. You can specify the volume by filesystem
#              label, by partition label, or by partition GUID number
#              (but NOT yet by filesystem UUID number).
#  loader    - identifies the boot loader file
#  initrd    - Specifies an initial RAM disk file
#  icon      - specifies a custom boot loader icon
#  ostype    - OS type code to determine boot options used by default
#  graphics  - set to "on" to enable graphics-mode boot (mainly useful
#              for MacOS) or "off" for text-mode boot.
#              Default is auto-detected from loader filename.
#  options   - sets options to be passed to the boot loader; use
#              quotes if more than one option and/or if any options
#              use characters that might be interpreted by the shell
#  disabled  - use alone or set to "yes" to disable this entry.
#
# Note that you can use either DOS/Windows/EFI-style backslashes (\)
# or Unix-style forward slashes (/) as directory separators. Either
# way, all file references are on the ESP from which rEFInd was
# launched.
# Use of quotes around parameters causes them to be interpreted as
# one keyword, and for parsing of special characters to be
# disabled. This is useful mainly with the "options" keyword.
# Use of quotes around parameters causes them to be interpreted as
# one keyword, and for parsing of special characters to be
# disabled. This is useful mainly with the "options" keyword.

# Windows entry (manual configuration for better control)
menuentry "Windows 11" {
    icon \EFI\refind\icons\os_win.png
    volume "SYSTEM"
    loader \EFI\Microsoft\Boot\bootmgfw.efi
    graphics on
    disabled
}

# Pop!_OS entry
menuentry "Pop!_OS 22.04 LTS" {
    icon \EFI\refind\icons\os_linux.png
    volume "SYSTEM"
    loader \EFI\systemd\systemd-bootx64.efi
    graphics on
    disabled
}

# Garuda Linux entry  
menuentry "Garuda Linux" {
    icon \EFI\refind\icons\os_linux.png
    volume "SYSTEM"
    loader \EFI\Garuda\grubx64.efi
    graphics on
    disabled
}
