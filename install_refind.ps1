# rEFInd Installation Script for Windows
# This script downloads and installs rEFInd boot manager

Write-Host "rEFInd Installation Script" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires administrator privileges. Please run as administrator." -ForegroundColor Red
    exit 1
}

# Create working directory
$workDir = "C:\temp\refind_install"
if (!(Test-Path $workDir)) {
    New-Item -ItemType Directory -Path $workDir -Force
}
Set-Location $workDir

Write-Host "Working directory: $workDir" -ForegroundColor Yellow

# Download rEFInd using curl (more reliable for large files)
$refindUrl = "https://sourceforge.net/projects/refind/files/0.14.2/refind-bin-0.14.2.zip/download"
$zipFile = "$workDir\refind-bin-0.14.2.zip"

Write-Host "Downloading rEFInd using curl..." -ForegroundColor Yellow
try {
    $curlCommand = "curl -L -o `"$zipFile`" `"$refindUrl`""
    Invoke-Expression $curlCommand

    if (Test-Path $zipFile) {
        $fileSize = (Get-Item $zipFile).Length
        Write-Host "Download completed successfully. File size: $([math]::Round($fileSize/1MB, 2)) MB" -ForegroundColor Green
    } else {
        throw "Download failed - file not found"
    }
} catch {
    Write-Host "Failed to download rEFInd: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Trying alternative download method..." -ForegroundColor Yellow

    # Fallback to PowerShell with different settings
    try {
        [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
        $webClient = New-Object System.Net.WebClient
        $webClient.DownloadFile($refindUrl, $zipFile)
        Write-Host "Alternative download completed successfully." -ForegroundColor Green
    } catch {
        Write-Host "All download methods failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Extract rEFInd
Write-Host "Extracting rEFInd..." -ForegroundColor Yellow
try {
    Expand-Archive -Path $zipFile -DestinationPath $workDir -Force
    Write-Host "Extraction completed successfully." -ForegroundColor Green
} catch {
    Write-Host "Failed to extract rEFInd: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "rEFInd download and extraction completed." -ForegroundColor Green

# Check if extraction was successful
$refindDir = "$workDir\refind-bin-0.14.2\refind"
if (!(Test-Path $refindDir)) {
    Write-Host "Error: rEFInd directory not found after extraction." -ForegroundColor Red
    exit 1
}

Write-Host "Found rEFInd directory: $refindDir" -ForegroundColor Green

# Mount the ESP
Write-Host "Mounting EFI System Partition..." -ForegroundColor Yellow
$espLetter = "R:"
try {
    $mountResult = mountvol $espLetter /S
    Write-Host "ESP mounted successfully at $espLetter" -ForegroundColor Green
} catch {
    Write-Host "Failed to mount ESP: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Create backup of current boot configuration
Write-Host "Creating backup of current boot configuration..." -ForegroundColor Yellow
$backupDir = "$workDir\backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

# Backup current EFI directory structure
if (Test-Path "${espLetter}\EFI") {
    try {
        Copy-Item -Path "${espLetter}\EFI" -Destination "$backupDir\EFI_backup" -Recurse -Force
        Write-Host "EFI directory backed up to: $backupDir\EFI_backup" -ForegroundColor Green
    } catch {
        Write-Host "Warning: Could not backup EFI directory: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Backup current boot configuration
try {
    bcdedit /export "$backupDir\bcd_backup.txt" | Out-Null
    Write-Host "Boot configuration backed up to: $backupDir\bcd_backup.txt" -ForegroundColor Green
} catch {
    Write-Host "Warning: Could not backup boot configuration: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "Backup completed. Proceeding with rEFInd installation..." -ForegroundColor Green

# Install rEFInd files
Write-Host "Installing rEFInd files to ESP..." -ForegroundColor Yellow

# Create rEFInd directory on ESP
$espRefindDir = "${espLetter}\EFI\refind"
if (!(Test-Path $espRefindDir)) {
    New-Item -ItemType Directory -Path $espRefindDir -Force | Out-Null
    Write-Host "Created rEFInd directory: $espRefindDir" -ForegroundColor Green
}

# Copy rEFInd files
try {
    # Copy main rEFInd binary (x64 version for modern systems)
    Copy-Item -Path "$refindDir\refind_x64.efi" -Destination "$espRefindDir\refind_x64.efi" -Force
    Write-Host "Copied rEFInd binary" -ForegroundColor Green

    # Copy configuration file
    Copy-Item -Path "$refindDir\refind.conf-sample" -Destination "$espRefindDir\refind.conf" -Force
    Write-Host "Copied configuration file" -ForegroundColor Green

    # Copy icons directory
    Copy-Item -Path "$refindDir\icons" -Destination "$espRefindDir\icons" -Recurse -Force
    Write-Host "Copied icons directory" -ForegroundColor Green

    # Copy drivers directory (x64 only)
    Copy-Item -Path "$refindDir\drivers_x64" -Destination "$espRefindDir\drivers_x64" -Recurse -Force
    Write-Host "Copied filesystem drivers" -ForegroundColor Green

    # Copy tools if they exist
    if (Test-Path "$refindDir\tools_x64") {
        Copy-Item -Path "$refindDir\tools_x64" -Destination "$espRefindDir\tools_x64" -Recurse -Force
        Write-Host "Copied tools directory" -ForegroundColor Green
    }

} catch {
    Write-Host "Error copying rEFInd files: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "rEFInd files installed successfully!" -ForegroundColor Green

# Register rEFInd with UEFI firmware
Write-Host "Registering rEFInd with UEFI firmware..." -ForegroundColor Yellow

try {
    # Add rEFInd as a boot option
    $bcdeditResult = bcdedit /create /d "rEFInd Boot Manager" /application bootsector
    if ($bcdeditResult -match '\{([^}]+)\}') {
        $refindGuid = $matches[1]
        Write-Host "Created rEFInd boot entry with GUID: {$refindGuid}" -ForegroundColor Green

        # Set the device and path for rEFInd
        bcdedit /set "{$refindGuid}" device partition=R:
        bcdedit /set "{$refindGuid}" path \EFI\refind\refind_x64.efi
        bcdedit /set "{$refindGuid}" description "rEFInd Boot Manager"

        # Add rEFInd to the firmware boot order (make it first)
        bcdedit /set "{fwbootmgr}" displayorder "{$refindGuid}" /addfirst

        Write-Host "rEFInd registered successfully with UEFI firmware!" -ForegroundColor Green
        Write-Host "rEFInd GUID: {$refindGuid}" -ForegroundColor Yellow
    } else {
        throw "Failed to extract GUID from bcdedit output"
    }
} catch {
    Write-Host "Error registering rEFInd with firmware: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "You may need to manually set rEFInd as the default boot option in your UEFI settings." -ForegroundColor Yellow
}

# Unmount ESP
Write-Host "Unmounting ESP..." -ForegroundColor Yellow
try {
    mountvol $espLetter /D
    Write-Host "ESP unmounted successfully." -ForegroundColor Green
} catch {
    Write-Host "Warning: Could not unmount ESP: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Final summary
Write-Host ""
Write-Host "=== rEFInd Installation Complete ===" -ForegroundColor Green
Write-Host "rEFInd has been installed to your EFI System Partition." -ForegroundColor White
Write-Host "Backup files are located at: $backupDir" -ForegroundColor Yellow
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Reboot your computer" -ForegroundColor White
Write-Host "2. rEFInd should appear as the boot manager" -ForegroundColor White
Write-Host "3. You should see options for Windows, Pop!_OS, and Garuda Linux" -ForegroundColor White
Write-Host "4. If rEFInd doesn't appear, check your UEFI settings" -ForegroundColor White
Write-Host ""
Write-Host "Configuration file location: R:\EFI\refind\refind.conf" -ForegroundColor Yellow
Write-Host "You can customize rEFInd by editing this file." -ForegroundColor White
Write-Host ""
Write-Host "Installation completed successfully!" -ForegroundColor Green
